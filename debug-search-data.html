<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索数据调试</title>
    <link rel="stylesheet" href="nav/css/style.css">
    <link rel="stylesheet" href="nav/css/themes.css">
    <style>
        body {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
        }
        
        .data-display {
            background: var(--surface-color);
            padding: 12px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .search-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .highlight-html {
            background: #ffeb3b;
            color: #d32f2f;
            font-weight: bold;
        }
        
        .test-input {
            width: 200px;
            padding: 8px;
            margin: 0 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }
    </style>
</head>
<body data-theme="jasmine-green">
    <div class="debug-container">
        <h1>搜索数据调试工具</h1>
        
        <div class="debug-section">
            <h3>搜索数据检查</h3>
            <button onclick="loadAndInspectData()">加载并检查搜索数据</button>
            <div id="dataInspection" class="data-display"></div>
        </div>

        <div class="debug-section">
            <h3>HTML内容检测</h3>
            <button onclick="detectHtmlContent()">检测HTML内容</button>
            <div id="htmlDetection" class="data-display"></div>
        </div>

        <div class="debug-section">
            <h3>搜索测试</h3>
            <input type="text" id="searchQuery" class="test-input" placeholder="输入搜索关键词" value="ss">
            <button onclick="performDebugSearch()">执行搜索</button>
            <div id="searchResults" class="data-display"></div>
        </div>

        <div class="debug-section">
            <h3>DOM属性检查</h3>
            <button onclick="inspectDomAttributes()">检查DOM属性</button>
            <div id="domInspection" class="data-display"></div>
        </div>
    </div>

    <script src="nav/js/utils.js"></script>
    <script src="nav/js/config.js"></script>
    <script src="nav/js/app.js"></script>
    <script src="nav/js/search.js"></script>
    
    <script>
        let navApp = null;
        let searchManager = null;

        // 初始化应用
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                navApp = new NavApp();
                await navApp.init();
                searchManager = navApp.searchManager;
                console.log('应用初始化完成');
            } catch (error) {
                console.error('应用初始化失败:', error);
            }
        });

        function loadAndInspectData() {
            if (!navApp || !searchManager) {
                document.getElementById('dataInspection').textContent = '应用未初始化';
                return;
            }

            const searchData = searchManager.searchData;
            const inspection = {
                totalSites: searchData.length,
                sampleSites: searchData.slice(0, 5).map(site => ({
                    id: site.id,
                    name: site.name,
                    description: site.description,
                    hasHtmlInName: /<[^>]*>/.test(site.name || ''),
                    hasHtmlInDescription: /<[^>]*>/.test(site.description || ''),
                    categoryName: site.categoryName
                })),
                allSitesWithHtml: searchData.filter(site => 
                    /<[^>]*>/.test(site.name || '') || /<[^>]*>/.test(site.description || '')
                ).map(site => ({
                    id: site.id,
                    name: site.name,
                    description: site.description,
                    categoryName: site.categoryName
                }))
            };

            document.getElementById('dataInspection').textContent = JSON.stringify(inspection, null, 2);
        }

        function detectHtmlContent() {
            if (!navApp || !searchManager) {
                document.getElementById('htmlDetection').textContent = '应用未初始化';
                return;
            }

            const searchData = searchManager.searchData;
            const htmlPattern = /<[^>]*>/g;
            const results = [];

            searchData.forEach(site => {
                const nameMatches = (site.name || '').match(htmlPattern);
                const descMatches = (site.description || '').match(htmlPattern);
                
                if (nameMatches || descMatches) {
                    results.push({
                        id: site.id,
                        name: site.name,
                        nameHtml: nameMatches,
                        description: site.description,
                        descHtml: descMatches,
                        categoryName: site.categoryName
                    });
                }
            });

            const output = results.length > 0 
                ? `发现 ${results.length} 个包含HTML的网站:\n\n${JSON.stringify(results, null, 2)}`
                : '未发现包含HTML标签的网站数据';

            document.getElementById('htmlDetection').textContent = output;
        }

        function performDebugSearch() {
            if (!searchManager) {
                document.getElementById('searchResults').textContent = '搜索管理器未初始化';
                return;
            }

            const query = document.getElementById('searchQuery').value;
            if (!query) {
                document.getElementById('searchResults').textContent = '请输入搜索关键词';
                return;
            }

            const results = searchManager.searchSites(query);
            const debugInfo = {
                query: query,
                resultCount: results.length,
                results: results.slice(0, 10).map(site => ({
                    id: site.id,
                    name: site.name,
                    description: site.description,
                    score: site.score,
                    matchType: site.matchType,
                    hasHtmlInName: /<[^>]*>/.test(site.name || ''),
                    hasHtmlInDescription: /<[^>]*>/.test(site.description || '')
                }))
            };

            document.getElementById('searchResults').textContent = JSON.stringify(debugInfo, null, 2);
        }

        function inspectDomAttributes() {
            const searchResultItems = document.querySelectorAll('.search-result-item');
            const spotlightItems = document.querySelectorAll('.spotlight-result-item');
            const siteCards = document.querySelectorAll('.site-card');

            const inspection = {
                searchResultItems: Array.from(searchResultItems).slice(0, 5).map(item => ({
                    siteId: item.getAttribute('data-site-id'),
                    siteName: item.getAttribute('data-site-name'),
                    hasHtmlInSiteName: /<[^>]*>/.test(item.getAttribute('data-site-name') || ''),
                    innerHTML: item.innerHTML.substring(0, 200) + '...'
                })),
                spotlightItems: Array.from(spotlightItems).slice(0, 5).map(item => ({
                    siteId: item.getAttribute('data-site-id'),
                    siteName: item.getAttribute('data-site-name'),
                    hasHtmlInSiteName: /<[^>]*>/.test(item.getAttribute('data-site-name') || '')
                })),
                siteCards: Array.from(siteCards).slice(0, 5).map(item => ({
                    siteId: item.getAttribute('data-site-id'),
                    siteName: item.getAttribute('data-site-name'),
                    hasHtmlInSiteName: /<[^>]*>/.test(item.getAttribute('data-site-name') || '')
                }))
            };

            document.getElementById('domInspection').textContent = JSON.stringify(inspection, null, 2);
        }

        // 添加一些调试工具
        window.debugSearchData = function() {
            if (searchManager) {
                console.log('搜索数据:', searchManager.searchData);
                return searchManager.searchData;
            }
            return null;
        };

        window.debugSearch = function(query) {
            if (searchManager) {
                const results = searchManager.searchSites(query);
                console.log(`搜索 "${query}" 的结果:`, results);
                return results;
            }
            return null;
        };
    </script>
</body>
</html>
