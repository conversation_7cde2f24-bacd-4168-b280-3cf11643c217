<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试</title>
    <link rel="stylesheet" href="nav/css/style.css">
    <link rel="stylesheet" href="nav/css/themes.css">
    <style>
        body {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .search-demo {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
        }
        
        .demo-search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 16px;
        }
        
        .demo-results {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .demo-result-item {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.15s ease;
        }
        
        .demo-result-item:hover {
            background-color: var(--surface-hover);
        }
        
        .demo-result-item.selected {
            background-color: var(--primary-color);
            color: white;
        }
        
        .demo-result-item.selected mark {
            background-color: rgba(255, 255, 255, 0.3);
            color: white;
        }
        
        .demo-result-item:last-child {
            border-bottom: none;
        }
        
        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .theme-switcher select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--card-background);
            color: var(--text-primary);
        }
        
        .instructions {
            background: var(--surface-color);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body data-theme="jasmine-green">
    <div class="theme-switcher">
        <select id="themeSelect">
            <option value="jasmine-green">清雅茉莉绿</option>
            <option value="ivory-light">日光象牙白</option>
            <option value="dark-obsidian">夜月玄玉黑</option>
            <option value="navy-blue">深邃海军蓝</option>
            <option value="star-moonlight-blue">星月流光蓝</option>
            <option value="light">浅色主题</option>
            <option value="dark">深色主题</option>
        </select>
    </div>

    <div class="test-container">
        <h1>搜索高亮冲突修复测试</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ol>
                <li>在搜索框中输入关键词（如"GitHub"、"开发"等）</li>
                <li>观察搜索结果中的高亮效果</li>
                <li>用鼠标选中搜索结果中的文字</li>
                <li>切换不同主题测试兼容性</li>
                <li>检查文本选中高亮与搜索高亮是否有良好的视觉区分</li>
            </ol>
        </div>

        <div class="search-demo">
            <h3>搜索演示</h3>
            <input type="text" class="demo-search-input" placeholder="输入关键词搜索..." id="searchInput">
            <div class="demo-results" id="searchResults"></div>
        </div>
    </div>

    <script>
        // 模拟搜索数据
        const mockData = [
            { name: "GitHub", description: "全球最大的代码托管平台，开发者协作的首选工具", category: "开发工具" },
            { name: "Stack Overflow", description: "程序员问答社区，解决编程问题的最佳去处", category: "开发工具" },
            { name: "MDN Web Docs", description: "Web开发文档和学习资源，前端开发必备", category: "文档" },
            { name: "Vue.js", description: "渐进式JavaScript框架，用于构建用户界面", category: "前端框架" },
            { name: "React", description: "用于构建用户界面的JavaScript库", category: "前端框架" },
            { name: "Node.js", description: "基于Chrome V8引擎的JavaScript运行时", category: "后端技术" },
            { name: "Visual Studio Code", description: "微软开发的免费代码编辑器", category: "开发工具" },
            { name: "Figma", description: "基于浏览器的协作式UI设计工具", category: "设计工具" }
        ];

        // 主题切换
        const themeSelect = document.getElementById('themeSelect');
        const body = document.body;

        themeSelect.addEventListener('change', function() {
            body.setAttribute('data-theme', this.value);
        });

        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');
        let selectedIndex = -1;

        function highlightText(text, query) {
            if (!query) return text;
            const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            return text.replace(regex, '<mark>$1</mark>');
        }

        function performSearch(query) {
            if (!query.trim()) {
                searchResults.innerHTML = '';
                return;
            }

            const results = mockData.filter(item => 
                item.name.toLowerCase().includes(query.toLowerCase()) ||
                item.description.toLowerCase().includes(query.toLowerCase()) ||
                item.category.toLowerCase().includes(query.toLowerCase())
            );

            if (results.length === 0) {
                searchResults.innerHTML = '<div class="demo-result-item">未找到匹配结果</div>';
                return;
            }

            const html = results.map((item, index) => `
                <div class="demo-result-item" data-index="${index}">
                    <div style="font-weight: 600; margin-bottom: 4px;">
                        ${highlightText(item.name, query)}
                    </div>
                    <div style="color: var(--text-secondary); font-size: 14px; margin-bottom: 2px;">
                        ${highlightText(item.description, query)}
                    </div>
                    <div style="color: var(--text-muted); font-size: 12px;">
                        ${highlightText(item.category, query)}
                    </div>
                </div>
            `).join('');

            searchResults.innerHTML = html;
            selectedIndex = -1;
        }

        function updateSelection() {
            const items = searchResults.querySelectorAll('.demo-result-item');
            items.forEach((item, index) => {
                item.classList.toggle('selected', index === selectedIndex);
            });
        }

        searchInput.addEventListener('input', (e) => {
            performSearch(e.target.value);
        });

        searchInput.addEventListener('keydown', (e) => {
            const items = searchResults.querySelectorAll('.demo-result-item');
            
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                updateSelection();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                updateSelection();
            }
        });

        // 点击选择
        searchResults.addEventListener('click', (e) => {
            const item = e.target.closest('.demo-result-item');
            if (item) {
                const index = parseInt(item.dataset.index);
                selectedIndex = index;
                updateSelection();
            }
        });

        // 初始化提示
        console.log('搜索测试页面已加载');
        console.log('请尝试搜索关键词，观察高亮效果');
    </script>
</body>
</html>
