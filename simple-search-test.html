<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单搜索测试</title>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        input, button {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>简单搜索测试</h1>
    
    <div class="test-section">
        <h3>1. 直接搜索测试</h3>
        <input type="text" id="directSearch" placeholder="输入搜索词" value="ss">
        <button onclick="testDirectSearch()">直接搜索</button>
        <div id="directResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 模拟数据测试</h3>
        <button onclick="testWithMockData()">使用模拟数据测试</button>
        <div id="mockResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 实际导航页面测试</h3>
        <button onclick="testWithRealData()">使用实际数据测试</button>
        <div id="realResult" class="result"></div>
    </div>

    <script>
        // 模拟搜索数据
        const mockSearchData = [
            {
                id: "test-1",
                name: "Clickhouse",
                description: "基于Clickhouse的日志中心",
                categoryName: "数据库"
            },
            {
                id: "test-2", 
                name: "GitHub",
                description: "代码托管平台",
                categoryName: "开发工具"
            },
            {
                id: "test-3",
                name: "测试网站",
                description: "这是一个测试网站",
                categoryName: "测试"
            }
        ];

        // 简单的搜索函数
        function simpleSearch(data, query) {
            const queryLower = query.toLowerCase();
            return data.filter(site => {
                return (site.name && site.name.toLowerCase().includes(queryLower)) ||
                       (site.description && site.description.toLowerCase().includes(queryLower));
            });
        }

        function testDirectSearch() {
            const query = document.getElementById('directSearch').value;
            const results = simpleSearch(mockSearchData, query);
            
            const output = {
                query: query,
                resultCount: results.length,
                results: results.map(site => ({
                    id: site.id,
                    name: site.name,
                    description: site.description,
                    hasHtmlInName: /<[^>]*>/.test(site.name || ''),
                    hasHtmlInDesc: /<[^>]*>/.test(site.description || '')
                }))
            };
            
            document.getElementById('directResult').textContent = JSON.stringify(output, null, 2);
        }

        function testWithMockData() {
            // 添加一些包含HTML的测试数据
            const mockDataWithHtml = [
                ...mockSearchData,
                {
                    id: "html-test-1",
                    name: '<mark class="pipeline-highlight-1">Clickhouse</mark>',
                    description: "包含HTML标签的测试数据",
                    categoryName: "测试"
                },
                {
                    id: "html-test-2",
                    name: "正常网站",
                    description: '<span class="highlighted">包含HTML的描述</span>',
                    categoryName: "测试"
                }
            ];

            const query = "ss";
            const results = simpleSearch(mockDataWithHtml, query);
            
            const output = {
                query: query,
                totalData: mockDataWithHtml.length,
                resultCount: results.length,
                allDataWithHtml: mockDataWithHtml.filter(site => 
                    /<[^>]*>/.test(site.name || '') || /<[^>]*>/.test(site.description || '')
                ),
                matchingResults: results.map(site => ({
                    id: site.id,
                    name: site.name,
                    description: site.description,
                    hasHtmlInName: /<[^>]*>/.test(site.name || ''),
                    hasHtmlInDesc: /<[^>]*>/.test(site.description || ''),
                    matchReason: getMatchReason(site, query)
                }))
            };
            
            document.getElementById('mockResult').textContent = JSON.stringify(output, null, 2);
        }

        function getMatchReason(site, query) {
            const queryLower = query.toLowerCase();
            const reasons = [];
            
            if (site.name && site.name.toLowerCase().includes(queryLower)) {
                reasons.push(`name contains "${query}"`);
            }
            if (site.description && site.description.toLowerCase().includes(queryLower)) {
                reasons.push(`description contains "${query}"`);
            }
            
            return reasons.join(', ');
        }

        async function testWithRealData() {
            try {
                // 尝试加载实际的导航数据
                const response = await fetch('./nav/data/foneshare-faci.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const data = await response.json();
                const allSites = [];
                
                // 提取所有网站
                function extractSites(categories) {
                    categories.forEach(category => {
                        if (category.sites) {
                            category.sites.forEach(site => {
                                allSites.push({
                                    ...site,
                                    categoryName: category.name
                                });
                            });
                        }
                        if (category.children) {
                            extractSites(category.children);
                        }
                    });
                }
                
                extractSites(data.categories || []);
                
                const query = "ss";
                const results = simpleSearch(allSites, query);
                
                const output = {
                    query: query,
                    totalSites: allSites.length,
                    resultCount: results.length,
                    sitesWithHtml: allSites.filter(site => 
                        /<[^>]*>/.test(site.name || '') || /<[^>]*>/.test(site.description || '')
                    ).length,
                    matchingResults: results.slice(0, 10).map(site => ({
                        id: site.id,
                        name: site.name,
                        description: site.description,
                        categoryName: site.categoryName,
                        hasHtmlInName: /<[^>]*>/.test(site.name || ''),
                        hasHtmlInDesc: /<[^>]*>/.test(site.description || ''),
                        matchReason: getMatchReason(site, query)
                    }))
                };
                
                document.getElementById('realResult').textContent = JSON.stringify(output, null, 2);
                
            } catch (error) {
                document.getElementById('realResult').className = 'result error';
                document.getElementById('realResult').textContent = `加载失败: ${error.message}`;
            }
        }

        // 页面加载时运行初始测试
        document.addEventListener('DOMContentLoaded', function() {
            testDirectSearch();
        });
    </script>
</body>
</html>
