<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML标签高亮测试</title>
    <link rel="stylesheet" href="nav/css/style.css">
    <link rel="stylesheet" href="nav/css/themes.css">
    <script src="nav/js/utils.js"></script>
    <style>
        body {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
        }
        
        .test-input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid var(--border-color);
            border-radius: 6px;
        }
        
        .test-result {
            padding: 12px;
            background: var(--surface-color);
            border-radius: 6px;
            margin: 10px 0;
            min-height: 40px;
        }
        
        .original-text {
            background: #f0f0f0;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
        }
    </style>
</head>
<body data-theme="jasmine-green">
    <div class="test-container">
        <h1>HTML标签高亮Bug修复测试</h1>
        
        <div class="test-section">
            <h3>问题重现测试</h3>
            <p>这个测试用于验证搜索高亮不会匹配HTML标签内的内容</p>
            
            <label>输入包含HTML标签的文本：</label>
            <input type="text" class="test-input" id="htmlInput" 
                   value='<mark class="pipeline-highlight-1">Clickhouse</mark> 数据库'>
            
            <label>搜索关键词：</label>
            <input type="text" class="test-input" id="searchKeyword" value="ss">
            
            <button onclick="testHighlight()">测试高亮</button>
            
            <div class="test-result">
                <strong>原始文本：</strong>
                <div class="original-text" id="originalText"></div>
                
                <strong>高亮结果：</strong>
                <div id="highlightResult"></div>
                
                <strong>清理后文本：</strong>
                <div id="cleanedText"></div>
                
                <strong>最终高亮结果：</strong>
                <div id="finalResult"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>管道搜索测试</h3>
            <label>管道查询：</label>
            <input type="text" class="test-input" id="pipelineQuery" value="Click|数据">
            
            <button onclick="testPipelineHighlight()">测试管道高亮</button>
            
            <div class="test-result">
                <strong>管道高亮结果：</strong>
                <div id="pipelineResult"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>预设测试用例</h3>
            <button onclick="runPresetTests()">运行预设测试</button>
            <div id="presetResults"></div>
        </div>
    </div>

    <script>
        // 模拟SearchManager的stripHtmlTags方法
        function stripHtmlTags(text) {
            if (!text) return text;
            return text.replace(/<[^>]*>/g, '');
        }

        // 模拟管道搜索高亮
        function highlightPipelineKeywords(text, pipelineQuery) {
            if (!text || !pipelineQuery) return text;

            // 清理HTML标签
            const cleanText = stripHtmlTags(text);

            // 检查是否为管道查询
            if (!pipelineQuery.includes('|')) {
                return highlightKeyword(cleanText, pipelineQuery);
            }

            // 解析管道查询中的所有关键词
            const terms = pipelineQuery.split('|').map(term => term.trim()).filter(term => term.length > 0);
            let highlightedText = cleanText;

            // 为每个关键词添加不同颜色的高亮
            terms.forEach((term, index) => {
                const colorClass = `pipeline-highlight-${(index % 3) + 1}`;
                const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                highlightedText = highlightedText.replace(regex, `<mark class="${colorClass}">$1</mark>`);
            });

            return highlightedText;
        }

        function testHighlight() {
            const htmlInput = document.getElementById('htmlInput').value;
            const keyword = document.getElementById('searchKeyword').value;
            
            document.getElementById('originalText').textContent = htmlInput;
            
            // 测试原始高亮函数（可能有问题的版本）
            const oldResult = htmlInput.replace(new RegExp(`(${keyword})`, 'gi'), '<mark>$1</mark>');
            document.getElementById('highlightResult').innerHTML = oldResult;
            
            // 测试清理后的文本
            const cleaned = stripHtmlTags(htmlInput);
            document.getElementById('cleanedText').textContent = cleaned;
            
            // 测试修复后的高亮函数
            const newResult = highlightKeyword(cleaned, keyword);
            document.getElementById('finalResult').innerHTML = newResult;
        }

        function testPipelineHighlight() {
            const htmlInput = document.getElementById('htmlInput').value;
            const pipelineQuery = document.getElementById('pipelineQuery').value;
            
            const result = highlightPipelineKeywords(htmlInput, pipelineQuery);
            document.getElementById('pipelineResult').innerHTML = result;
        }

        function runPresetTests() {
            const testCases = [
                {
                    text: '<mark class="pipeline-highlight-1">Clickhouse</mark> 数据库',
                    keyword: 'ss',
                    expected: 'Clickhouse 数据库' // 不应该高亮ss
                },
                {
                    text: '<span class="highlighted">GitHub</span> 代码托管',
                    keyword: 'Git',
                    expected: '<mark>Git</mark>Hub 代码托管'
                },
                {
                    text: '正常文本 Clickhouse',
                    keyword: 'Click',
                    expected: '正常文本 <mark>Click</mark>house'
                }
            ];

            let resultsHtml = '';
            testCases.forEach((testCase, index) => {
                const cleaned = stripHtmlTags(testCase.text);
                const result = highlightKeyword(cleaned, testCase.keyword);
                
                resultsHtml += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                        <strong>测试 ${index + 1}:</strong><br>
                        原始: <code>${testCase.text}</code><br>
                        关键词: <code>${testCase.keyword}</code><br>
                        结果: ${result}<br>
                        状态: ${result.includes('<mark>') ? '✅ 正常高亮' : '⚠️ 无高亮'}
                    </div>
                `;
            });

            document.getElementById('presetResults').innerHTML = resultsHtml;
        }

        // 页面加载时运行初始测试
        document.addEventListener('DOMContentLoaded', function() {
            testHighlight();
            testPipelineHighlight();
        });
    </script>
</body>
</html>
