/**
 * 搜索管理器
 * 负责处理全局搜索、快捷键支持和实时结果显示
 */
class SearchManager {
    constructor(navApp) {
        this.navApp = navApp;
        this.searchInput = null;
        this.searchResults = null;
        this.searchWrapper = null;
        this.currentQuery = '';
        this.isSearchVisible = false;
        this.isFiltersVisible = false;
        this.selectedIndex = -1;
        this.searchData = [];

        // 筛选状态
        this.filters = {
            category: '',
            tags: new Set()
        };

        // 新增DOM元素
        this.searchFilterBtn = null;
        this.searchFilters = null;
        this.categoryFilter = null;
        this.tagFilters = null;
        this.clearFiltersBtn = null;

        // 标签筛选器使用状态
        this.hasUsedTagFilters = false;

        // 防抖搜索 - 使用配置文件中的防抖时间
        const debounceTime = (window.NavSphereConfig?.search?.debounceTime) || 300;
        this.debouncedSearch = debounce(this.performSearch.bind(this), debounceTime);

        this.init();
    }
    
    /**
     * 初始化搜索管理器
     */
    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupSearchElements();
                this.bindEvents();
            });
        } else {
            this.setupSearchElements();
            this.bindEvents();
        }
    }
    
    /**
     * 设置搜索元素
     */
    setupSearchElements() {
        this.searchInput = document.getElementById('searchInput');
        this.searchResults = document.getElementById('searchResults');
        this.searchWrapper = document.querySelector('.search-wrapper');
        this.searchFilterBtn = document.getElementById('searchFilterBtn');
        this.searchFilters = document.getElementById('searchFilters');
        this.categoryFilter = document.getElementById('categoryFilter');
        this.tagFilters = document.getElementById('tagFilters');
        this.clearFiltersBtn = document.getElementById('clearFilters');

        if (!this.searchInput || !this.searchResults) {
            console.error('Essential search elements not found');
            return;
        }

        // 初始化筛选器（如果元素存在）
        if (this.categoryFilter && this.tagFilters) {
            this.initializeFilters();
        }
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        if (!this.searchInput) return;
        
        // 搜索输入事件
        this.searchInput.addEventListener('input', (e) => {
            const inputValue = e.target.value;
            this.currentQuery = inputValue.trim();

            // 开始输入时隐藏筛选面板
            if (this.isFiltersVisible) {
                this.hideFilters();
            }

            if (this.currentQuery) {
                this.debouncedSearch(this.currentQuery);
            } else {
                // 当输入为空时，立即隐藏搜索结果并重置搜索状态
                this.hideSearchResults();
                this.resetSearch();
                // 取消任何待执行的防抖搜索
                if (this.debouncedSearch.cancel) {
                    this.debouncedSearch.cancel();
                }
            }
        });
        
        // 搜索框焦点事件
        this.searchInput.addEventListener('focus', () => {
            // 隐藏筛选面板
            this.hideFilters();

            if (this.currentQuery) {
                this.showSearchResults();
            } else if (this.filters.category || this.filters.tags.size > 0) {
                // 如果没有搜索查询但有筛选条件，显示筛选结果
                this.showFilteredResults();
            }
        });
        
        // 搜索框失焦事件（延迟隐藏，允许点击结果）
        this.searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                this.hideSearchResults();
            }, 200);
        });
        
        // 键盘导航
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });
        
        // 全局快捷键
        document.addEventListener('keydown', (e) => {
            this.handleGlobalKeyboard(e);
        });
        
        // 筛选按钮事件
        if (this.searchFilterBtn) {
            this.searchFilterBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFilters();
            });
            
            // 更新筛选按钮标题，包含快捷键信息
            this.updateFilterButtonTitle();
        }

        // 清除筛选按钮事件
        if (this.clearFiltersBtn) {
            this.clearFiltersBtn.addEventListener('click', () => {
                // 使用完整清除，保持与快捷键一致的行为
                this.clearAllFiltersWithFeedback();
            });
        }

        // 自定义分类筛选器事件
        if (this.categoryFilter) {
            this.initCustomSelect();
        }

        // 点击外部隐藏搜索结果和筛选器
        document.addEventListener('click', (e) => {
            const isInsideSearch = this.searchInput && this.searchInput.contains(e.target);
            const isInsideResults = this.searchResults && this.searchResults.contains(e.target);
            const isInsideFilters = this.searchFilters && this.searchFilters.contains(e.target);
            const isInsideFilterBtn = this.searchFilterBtn && this.searchFilterBtn.contains(e.target);

            if (!isInsideSearch && !isInsideResults && !isInsideFilters && !isInsideFilterBtn) {
                this.hideSearchResults();
                this.hideFilters();
            }
        });

        // ESC键关闭筛选器
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isFiltersVisible) {
                e.preventDefault();
                this.hideFilters();
            }
        });
    }
    
    /**
     * 处理全局键盘事件
     * @param {KeyboardEvent} e 键盘事件
     */
    handleGlobalKeyboard(e) {
        // 空格键唤起 Spotlight 搜索
        if (e.code === 'Space') {
            // 检查是否应该处理空格键
            if (this.shouldHandleSpaceKey()) {
                this.handleSpaceKey(e);
            }
            // Markdown管理器现在在capture阶段处理，如果模态框打开，这里不会执行到
            return;
        }
        
        // 使用平台检测来处理快捷键 - ⌘K / Ctrl+K 清除搜索内容
        if (typeof Platform !== 'undefined' && Platform.isShortcut(e, 'K')) {
            e.preventDefault();
            this.clearSearch();
            return;
        }
        
        // Ctrl/Cmd+Shift+K 快速清除所有筛选条件
        if (typeof Platform !== 'undefined' && this.isShiftShortcut(e, 'K')) {
            e.preventDefault();
            this.clearAllFiltersWithFeedback();
        }
        
        // Escape 清空搜索（只在搜索状态下生效）
        if (e.key === 'Escape' && this.isSearching()) {
            e.preventDefault();
            this.clearSearch();
        }
        
        // Escape 清除筛选器（当筛选器打开时）
        if (e.key === 'Escape' && this.isFiltersVisible) {
            e.preventDefault();
            this.hideFilters();
        }
    }
    
    /**
     * 处理键盘导航
     * @param {KeyboardEvent} e 键盘事件
     */
    handleKeyboardNavigation(e) {
        if (!this.isSearchVisible) return;
        
        const resultItems = this.searchResults.querySelectorAll('.search-result-item');
        const itemCount = resultItems.length;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, itemCount - 1);
                this.updateSelectedItem(resultItems);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateSelectedItem(resultItems);
                break;
                
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && resultItems[this.selectedIndex]) {
                    this.selectSearchResult(resultItems[this.selectedIndex]);
                }
                break;
                
            case 'Escape':
                this.hideSearchResults();
                this.searchInput.blur();
                break;
        }
    }
    
    /**
     * 更新选中的搜索结果项
     * @param {NodeList} resultItems 结果项列表
     */
    updateSelectedItem(resultItems) {
        // 移除所有选中状态
        resultItems.forEach(item => item.classList.remove('selected'));
        
        // 添加当前选中状态
        if (this.selectedIndex >= 0 && resultItems[this.selectedIndex]) {
            resultItems[this.selectedIndex].classList.add('selected');
            resultItems[this.selectedIndex].scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    }
    
    /**
     * 聚焦搜索框
     */
    focusSearch() {
        if (this.searchInput) {
            this.searchInput.focus();
            this.searchInput.select();
        }
    }
    
    /**
     * 清空搜索
     */
    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
            this.currentQuery = '';
            this.hideSearchResults();
            this.resetSearch();

            // 取消任何待执行的防抖搜索
            if (this.debouncedSearch.cancel) {
                this.debouncedSearch.cancel();
            }

            // 清除主应用的悬停状态
            if (window.navApp && typeof window.navApp.clearHoveredCard === 'function') {
                window.navApp.clearHoveredCard();
            }

            // 确保搜索框失去焦点，触发相关事件
            if (this.searchInput === document.activeElement) {
                this.searchInput.blur();
            }
        }
    }
    
    /**
     * 重置搜索状态
     */
    resetSearch() {
        this.selectedIndex = -1;

        // 清除主应用的悬停状态
        if (window.navApp && typeof window.navApp.clearHoveredCard === 'function') {
            window.navApp.clearHoveredCard();
        }

        if (this.navApp && typeof this.navApp.showAllSites === 'function') {
            this.navApp.showAllSites();
        }
    }
    
    /**
     * 执行搜索
     * @param {string} query 搜索查询
     */
    performSearch(query) {
        if (!query) {
            this.hideSearchResults();
            return;
        }
        
        const results = this.searchSites(query);
        this.displaySearchResults(results, query);
        this.showSearchResults();
        
        // 重置选中索引
        this.selectedIndex = -1;
        
        console.log(`Search performed for: "${query}", found ${results.length} results`);
    }
    
    /**
     * 搜索网站
     * @param {string} query 搜索查询
     * @returns {Array} 搜索结果
     */
    searchSites(query) {
        if (!this.searchData || this.searchData.length === 0) {
            return [];
        }

        // 检查是否为管道搜索
        if (this.isPipelineQuery(query)) {
            return this.performPipelineSearch(query);
        }

        // 普通搜索逻辑保持不变
        return this.performSingleSearch(query, this.searchData);
    }

    /**
     * 检查是否为管道查询
     * @param {string} query 搜索查询
     * @returns {boolean} 是否为管道查询
     */
    isPipelineQuery(query) {
        return query.includes('|') && query.split('|').length > 1;
    }

    /**
     * 执行管道搜索
     * @param {string} query 管道搜索查询
     * @returns {Array} 搜索结果
     */
    performPipelineSearch(query) {
        // 解析管道查询
        const terms = query.split('|').map(term => term.trim()).filter(term => term.length > 0);

        if (terms.length === 0) {
            return [];
        }

        console.log(`Pipeline search: ${terms.join(' | ')}`);

        let currentResults = this.searchData;
        const pipelineSteps = [];

        // 逐层过滤
        for (let i = 0; i < terms.length; i++) {
            const term = terms[i];
            const stepResults = this.performSingleSearch(term, currentResults);

            pipelineSteps.push({
                term: term,
                resultCount: stepResults.length,
                step: i + 1
            });

            if (stepResults.length === 0) {
                console.log(`Pipeline search stopped at step ${i + 1}: no results for "${term}"`);
                break;
            }

            // 更新当前结果集，为下一层过滤做准备
            currentResults = stepResults;
        }

        // 为管道搜索结果添加特殊标记和加成
        const finalResults = currentResults.map(site => ({
            ...site,
            score: site.score * 1.2, // 管道搜索加成
            matchType: 'pipeline',
            pipelineSteps: pipelineSteps,
            pipelineQuery: query
        }));

        console.log(`Pipeline search completed: ${finalResults.length} results after ${pipelineSteps.length} steps`);

        return finalResults;
    }

    /**
     * 执行单次搜索（原有搜索逻辑）
     * @param {string} query 搜索查询
     * @param {Array} searchData 搜索数据源
     * @returns {Array} 搜索结果
     */
    performSingleSearch(query, searchData) {
        const queryLower = query.toLowerCase();
        const results = [];

        searchData.forEach(site => {
            // 应用筛选器
            if (!this.passesFilters(site)) {
                return;
            }

            let score = 0;
            let matchType = '';

            // 标题匹配（权重最高）
            if (site.name && site.name.toLowerCase().includes(queryLower)) {
                score += site.name.toLowerCase() === queryLower ? 100 : 80;
                matchType = 'title';
            }

            // 描述匹配
            if (site.description && site.description.toLowerCase().includes(queryLower)) {
                score += 40;
                if (!matchType) matchType = 'description';
            }

            // 标签匹配（改进版）
            if (site.tags && site.tags.length > 0) {
                let tagScore = 0;
                let hasTagMatch = false;

                site.tags.forEach(tag => {
                    const tagLower = tag.toLowerCase();
                    if (tagLower === queryLower) {
                        // 完全匹配标签
                        tagScore += 60;
                        hasTagMatch = true;
                    } else if (tagLower.includes(queryLower)) {
                        // 部分匹配标签
                        tagScore += 35;
                        hasTagMatch = true;
                    }
                });

                if (hasTagMatch) {
                    score += tagScore;
                    if (!matchType) matchType = 'tags';
                }
            }

            // URL匹配
            if (site.url && site.url.toLowerCase().includes(queryLower)) {
                score += 20;
                if (!matchType) matchType = 'url';
            }

            // 分类名称匹配
            if (site.categoryName && site.categoryName.toLowerCase().includes(queryLower)) {
                score += 15;
                if (!matchType) matchType = 'category';
            }

            // 首字母匹配（新增功能）
            if (score === 0 || score < 50) { // 只有在没有高分匹配时才进行首字母匹配
                let initialScore = 0;
                let hasInitialMatch = false;

                // 标题首字母匹配（权重最高）
                if (site.name) {
                    const titleInitialMatch = matchInitials(queryLower, site.name);
                    if (titleInitialMatch.isMatch) {
                        initialScore += titleInitialMatch.score * 70; // 标题首字母匹配权重70
                        hasInitialMatch = true;
                        if (!matchType) matchType = 'initial-title';
                    }
                }

                // 描述首字母匹配
                if (site.description) {
                    const descInitialMatch = matchInitials(queryLower, site.description);
                    if (descInitialMatch.isMatch) {
                        initialScore += descInitialMatch.score * 30; // 描述首字母匹配权重30
                        hasInitialMatch = true;
                        if (!matchType) matchType = 'initial-desc';
                    }
                }

                // 标签首字母匹配
                if (site.tags && site.tags.length > 0) {
                    let tagInitialScore = 0;
                    let hasTagInitialMatch = false;

                    site.tags.forEach(tag => {
                        const tagInitialMatch = matchInitials(queryLower, tag);
                        if (tagInitialMatch.isMatch) {
                            tagInitialScore += tagInitialMatch.score * 40; // 标签首字母匹配权重40
                            hasTagInitialMatch = true;
                        }
                    });

                    if (hasTagInitialMatch) {
                        initialScore += tagInitialScore;
                        hasInitialMatch = true;
                        if (!matchType) matchType = 'initial-tags';
                    }
                }

                // 分类名称首字母匹配
                if (site.categoryName) {
                    const categoryInitialMatch = matchInitials(queryLower, site.categoryName);
                    if (categoryInitialMatch.isMatch) {
                        initialScore += categoryInitialMatch.score * 20; // 分类首字母匹配权重20
                        hasInitialMatch = true;
                        if (!matchType) matchType = 'initial-category';
                    }
                }

                // 如果有首字母匹配，更新分数
                if (hasInitialMatch) {
                    // 如果之前没有匹配或首字母匹配分数更高，使用首字母匹配分数
                    if (score === 0 || initialScore > score) {
                        score = initialScore;
                    }
                }
            }

            // 模糊匹配（作为最后的备选）
            if (score === 0) {
                const fuzzyScore = fuzzySearch(queryLower, site.name.toLowerCase()) * 25;
                if (fuzzyScore > 10) {
                    score = fuzzyScore;
                    matchType = 'fuzzy';
                }
            }

            if (score > 0) {
                results.push({
                    ...site,
                    score,
                    matchType
                });
            }
        });

        // 获取配置中的最大结果数，默认为50（提高默认值以显示更多结果）
        const maxResults = (window.NavSphereConfig?.search?.maxResults) || 50;

        // 按分数排序并限制结果数量
        return results.sort((a, b) => b.score - a.score).slice(0, maxResults);
    }
    
    /**
     * 显示搜索结果
     * @param {Array} results 搜索结果
     * @param {string} query 搜索查询
     */
    displaySearchResults(results, query) {
        if (!this.searchResults) return;

        if (results.length === 0) {
            // 检查是否为管道搜索
            const isPipeline = this.isPipelineQuery(query);
            this.searchResults.innerHTML = `
                <div class="search-no-results">
                    <i class="fas fa-search"></i>
                    <span>没有找到匹配的网站</span>
                    ${isPipeline ? '<div class="pipeline-hint">尝试减少管道过滤条件或调整搜索词</div>' : ''}
                </div>
            `;
            return;
        }

        // 检查是否有管道搜索结果，如果有则显示管道信息
        const hasPipelineResults = results.some(site => site.matchType === 'pipeline');
        let pipelineInfoHtml = '';

        if (hasPipelineResults) {
            const firstPipelineResult = results.find(site => site.matchType === 'pipeline');
            if (firstPipelineResult && firstPipelineResult.pipelineSteps) {
                pipelineInfoHtml = this.renderPipelineInfo(firstPipelineResult.pipelineSteps, results.length);
            }
        }

        const resultsHtml = results.map(site => {
            // 确定数据属性
            let dataAttrs = `data-site-id="${site.id}" data-site-name="${site.name}"`;
            if (site.markdownFile) {
                // Markdown 文档
                dataAttrs += ` data-markdown-file="${site.markdownFile}"`;
            }
            if (site.url) {
                // 外部链接
                dataAttrs += ` data-url="${site.url}"`;
            }

            return `
                <div class="search-result-item ${site.matchType === 'pipeline' ? 'pipeline-result' : ''}" ${dataAttrs}>
                    <div class="search-result-title">
                        ${this.highlightPipelineKeywords(site.name, site.matchType === 'pipeline' ? site.pipelineQuery : query)}
                        ${this.renderMatchTypeBadge(site.matchType)}
                    </div>
                    <div class="search-result-desc">
                        ${this.highlightPipelineKeywords(site.description || '', site.matchType === 'pipeline' ? site.pipelineQuery : query)}
                    </div>
                    <div class="search-result-category">
                        <i class="fas fa-folder"></i>
                        ${site.categoryName}
                    </div>
                    ${this.renderSearchResultTags(site.tags, site.matchType === 'pipeline' ? site.pipelineQuery : query)}
                </div>
            `;
        }).join('');

        this.searchResults.innerHTML = pipelineInfoHtml + resultsHtml;
        
        // 绑定点击事件
        this.searchResults.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', () => {
                this.selectSearchResult(item);
            });
            
            item.addEventListener('mouseenter', () => {
                // 更新选中索引
                const items = this.searchResults.querySelectorAll('.search-result-item');
                this.selectedIndex = Array.from(items).indexOf(item);
                this.updateSelectedItem(items);
            });
        });
    }
    
    /**
     * 渲染匹配类型徽章
     * @param {string} matchType 匹配类型
     * @returns {string} 徽章HTML
     */
    renderMatchTypeBadge(matchType) {
        const matchTypeMap = {
            'title': { text: '标题', icon: 'fas fa-heading', color: 'primary' },
            'description': { text: '描述', icon: 'fas fa-align-left', color: 'secondary' },
            'tags': { text: '标签', icon: 'fas fa-tags', color: 'success' },
            'url': { text: 'URL', icon: 'fas fa-link', color: 'info' },
            'category': { text: '分类', icon: 'fas fa-folder', color: 'warning' },
            'initial-title': { text: '标题首字母', icon: 'fas fa-font', color: 'primary' },
            'initial-desc': { text: '描述首字母', icon: 'fas fa-text-height', color: 'secondary' },
            'initial-tags': { text: '标签首字母', icon: 'fas fa-hashtag', color: 'success' },
            'initial-category': { text: '分类首字母', icon: 'fas fa-folder-open', color: 'warning' },
            'fuzzy': { text: '模糊', icon: 'fas fa-search', color: 'muted' },
            'pipeline': { text: '管道过滤', icon: 'fas fa-filter', color: 'pipeline' }
        };

        const match = matchTypeMap[matchType];
        if (!match) return '';

        return `<span class="search-match-badge badge-${match.color}" title="匹配类型: ${match.text}">
            <i class="${match.icon}"></i>
            ${match.text}
        </span>`;
    }

    /**
     * 渲染管道搜索信息
     * @param {Array} pipelineSteps 管道步骤
     * @param {number} finalResultCount 最终结果数量
     * @returns {string} 管道信息HTML
     */
    renderPipelineInfo(pipelineSteps, finalResultCount) {
        if (!pipelineSteps || pipelineSteps.length === 0) {
            return '';
        }

        const stepsHtml = pipelineSteps.map((step, index) => {
            const isLast = index === pipelineSteps.length - 1;
            return `
                <div class="pipeline-step">
                    <span class="pipeline-step-number">${step.step}</span>
                    <span class="pipeline-step-term">"${step.term}"</span>
                    <span class="pipeline-step-count">${step.resultCount} 个结果</span>
                    ${!isLast ? '<i class="fas fa-arrow-right pipeline-arrow"></i>' : ''}
                </div>
            `;
        }).join('');

        return `
            <div class="pipeline-search-info">
                <div class="pipeline-header">
                    <i class="fas fa-filter"></i>
                    <span>管道过滤搜索</span>
                    <span class="pipeline-final-count">最终 ${finalResultCount} 个结果</span>
                </div>
                <div class="pipeline-steps">
                    ${stepsHtml}
                </div>
            </div>
        `;
    }

    /**
     * 高亮管道搜索关键词
     * @param {string} text 原文本
     * @param {string} pipelineQuery 管道查询
     * @returns {string} 高亮后的HTML
     */
    highlightPipelineKeywords(text, pipelineQuery) {
        if (!text || !pipelineQuery) return text;

        // 如果不是管道查询，使用普通高亮
        if (!this.isPipelineQuery(pipelineQuery)) {
            return highlightKeyword(text, pipelineQuery);
        }

        // 清理可能存在的HTML标签，确保不会重复高亮
        const cleanText = this.stripHtmlTags(text);

        // 解析管道查询中的所有关键词
        const terms = pipelineQuery.split('|').map(term => term.trim()).filter(term => term.length > 0);
        let highlightedText = cleanText;

        // 为每个关键词添加不同颜色的高亮
        terms.forEach((term, index) => {
            const colorClass = `pipeline-highlight-${(index % 3) + 1}`;
            const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            highlightedText = highlightedText.replace(regex, `<mark class="${colorClass}">$1</mark>`);
        });

        return highlightedText;
    }

    /**
     * 清理HTML标签
     * @param {string} text 包含HTML的文本
     * @returns {string} 清理后的纯文本
     */
    stripHtmlTags(text) {
        if (!text) return text;

        // 移除HTML标签，但保留文本内容
        return text.replace(/<[^>]*>/g, '');
    }

    /**
     * 渲染搜索结果中的标签
     * @param {Array} tags 标签数组
     * @param {string} query 搜索查询
     * @returns {string} 标签HTML
     */
    renderSearchResultTags(tags, query) {
        if (!tags || tags.length === 0) {
            return '';
        }

        const tagsHtml = tags.map(tag => {
            let isHighlighted = false;
            let highlightedTag = tag;

            // 检查是否为管道查询
            if (this.isPipelineQuery(query)) {
                // 管道查询：检查是否匹配任一管道条件
                const terms = query.split('|').map(term => term.trim()).filter(term => term.length > 0);
                for (const term of terms) {
                    if (tag.toLowerCase().includes(term.toLowerCase())) {
                        isHighlighted = true;
                        highlightedTag = this.highlightPipelineKeywords(tag, query);
                        break;
                    }
                }
            } else {
                // 普通查询
                const queryLower = query.toLowerCase();
                isHighlighted = tag.toLowerCase().includes(queryLower);
                highlightedTag = isHighlighted ? highlightKeyword(tag, query) : tag;
            }

            const highlightClass = isHighlighted ? 'highlighted' : '';
            return `<span class="search-result-tag ${highlightClass}">${highlightedTag}</span>`;
        }).join('');

        return `<div class="search-result-tags">${tagsHtml}</div>`;
    }
    
    /**
     * 选择搜索结果
     * @param {Element} resultItem 结果项元素
     */
    selectSearchResult(resultItem) {
        const url = resultItem.dataset.url;
        const markdownFile = resultItem.dataset.markdownFile;
        const siteId = resultItem.dataset.siteId;
        const siteName = resultItem.dataset.siteName;

        // 检查链接和markdown内容的存在情况
        const hasUrl = url && url.trim() !== '';
        const hasMarkdown = markdownFile && markdownFile.trim() !== '';

        // 根据需求优先级处理：
        // 1. 同时存在链接和markdown时，优先打开链接
        // 2. 仅存在链接时，打开链接
        // 3. 仅存在markdown时，打开markdown
        if (hasUrl) {
            // 有链接时优先打开链接（无论是否同时有markdown）
            window.open(formatUrl(url), '_blank');
            this.hideSearchResults();

            // 记录点击统计
            this.recordSearchClick(siteId, this.currentQuery, 'external');
        } else if (hasMarkdown) {
            // 仅有markdown时打开文档
            if (this.navApp && this.navApp.markdownManager) {
                this.navApp.markdownManager.loadAndShow(markdownFile, siteName || '文档');
            } else {
                console.error('Markdown manager not available');
            }
            this.hideSearchResults();

            // 记录点击统计
            this.recordSearchClick(siteId, this.currentQuery, 'markdown');
        }
    }
    
    /**
     * 记录搜索点击统计
     * @param {string} siteId 网站ID
     * @param {string} query 搜索查询
     * @param {string} type 点击类型：'markdown' | 'external'
     */
    recordSearchClick(siteId, query, type = 'external') {
        // 这里可以添加统计逻辑
        console.log(`Search click: ${siteId}, query: ${query}, type: ${type}`);
    }
    
    /**
     * 显示搜索结果
     */
    showSearchResults() {
        if (this.searchResults) {
            this.searchResults.style.display = 'block';
            this.isSearchVisible = true;
        }
    }
    
    /**
     * 隐藏搜索结果
     */
    hideSearchResults() {
        if (this.searchResults) {
            this.searchResults.style.display = 'none';
            this.isSearchVisible = false;
            this.selectedIndex = -1;
        }
    }
    
    /**
     * 处理空格键的唤起/关闭搜索栏功能
     * @param {KeyboardEvent} e 键盘事件
     */
    handleSpaceKey(e) {
        e.preventDefault();

        // 创建或显示 Spotlight 风格的搜索覆盖层
        this.showSpotlightSearch();
    }
    
    /**
     * 判断是否应该处理空格键
     * @returns {boolean} 是否应该处理
     */
    shouldHandleSpaceKey() {
        // 如果有元素聚焦且是输入类元素，不处理空格键
        const activeElement = document.activeElement;
        if (activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        )) {
            return false;
        }

        // 如果 Markdown 模态框打开，空格键应该用于关闭模态框
        if (window.navApp && window.navApp.markdownManager && window.navApp.markdownManager.isModalOpen()) {
            return false;
        }

        // 检查主应用是否有悬停的卡片（优先使用主应用的状态）
        if (window.navApp && window.navApp.hoveredCard) {
            return false;
        }

        // 备用检查：如果鼠标悬停在卡片上，不处理空格键
        const hoveredCard = document.querySelector('.site-card:hover');
        if (hoveredCard) {
            return false;
        }

        return true;
    }
    
    /**
     * 显示 Spotlight 风格的搜索界面
     */
    showSpotlightSearch() {
        // 检查是否已存在
        let spotlightOverlay = document.getElementById('spotlightOverlay');
        
        if (!spotlightOverlay) {
            this.createSpotlightOverlay();
            spotlightOverlay = document.getElementById('spotlightOverlay');
        }
        
        // 显示覆盖层
        spotlightOverlay.style.display = 'flex';
        spotlightOverlay.classList.add('show');
        
        // 聚焦输入框
        const spotlightInput = document.getElementById('spotlightInput');
        if (spotlightInput) {
            setTimeout(() => {
                spotlightInput.focus();
                spotlightInput.select();
            }, 100);
        }
        
        // 阻止背景滚动
        document.body.style.overflow = 'hidden';
        
        console.log('Spotlight 搜索已唤起');
    }
    
    /**
     * 创建 Spotlight 搜索覆盖层
     */
    createSpotlightOverlay() {
        // 创建覆盖层
        const spotlightOverlay = document.createElement('div');
        spotlightOverlay.id = 'spotlightOverlay';
        spotlightOverlay.className = 'spotlight-overlay';
        
        // 创建搜索容器
        const spotlightContainer = document.createElement('div');
        spotlightContainer.className = 'spotlight-container';
        
        // 创建搜索输入框
        const spotlightInput = document.createElement('input');
        spotlightInput.id = 'spotlightInput';
        spotlightInput.className = 'spotlight-input';
        spotlightInput.type = 'text';
        spotlightInput.placeholder = '搜索网站名称、描述、标签...';
        spotlightInput.autocomplete = 'off';
        
        // 创建搜索结果容器
        const spotlightResults = document.createElement('div');
        spotlightResults.id = 'spotlightResults';
        spotlightResults.className = 'spotlight-results';
        
        // 组装结构
        spotlightContainer.appendChild(spotlightInput);
        spotlightContainer.appendChild(spotlightResults);
        spotlightOverlay.appendChild(spotlightContainer);
        document.body.appendChild(spotlightOverlay);
        
        // 绑定事件
        this.bindSpotlightEvents(spotlightInput, spotlightResults, spotlightOverlay);
        
        // 添加样式
        this.addSpotlightStyles();
    }
    
    /**
     * 绑定 Spotlight 搜索事件
     */
    bindSpotlightEvents(input, results, overlay) {
        // 搜索输入事件
        input.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query) {
                this.performSpotlightSearch(query, results);
            } else {
                results.innerHTML = '';
                results.style.display = 'none';
            }
        });

        // 键盘导航
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                this.hideSpotlightSearch();
            } else if (e.key === ' ' && e.target.value.trim() === '') {
                // 空格键且输入框为空时，关闭搜索栏
                e.preventDefault();
                this.hideSpotlightSearch();
            } else if (typeof Platform !== 'undefined' && Platform.isShortcut(e, 'K')) {
                // ⌘+K 或 Ctrl+K 清除搜索框内容
                e.preventDefault();
                input.value = '';
                results.innerHTML = '';
                results.style.display = 'none';
                this.selectedIndex = -1;
            } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp' || e.key === 'Enter') {
                this.handleSpotlightKeyboard(e, results);
            }
        });

        // 点击覆盖层关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.hideSpotlightSearch();
            }
        });
    }
    
    /**
     * 执行 Spotlight 搜索
     */
    performSpotlightSearch(query, resultsContainer) {
        const results = this.searchSites(query);
        this.displaySpotlightResults(results, query, resultsContainer);
    }
    
    /**
     * 显示 Spotlight 搜索结果
     */
    displaySpotlightResults(results, query, container) {
        if (results.length === 0) {
            container.innerHTML = `
                <div class="spotlight-no-results">
                    <i class="fas fa-search"></i>
                    <span>没有找到匹配的网站</span>
                </div>
            `;
            container.style.display = 'block';
            return;
        }
        
        const resultsHtml = results.map((site, index) => {
            let dataAttrs = `data-site-id="${site.id}" data-site-name="${site.name}" data-index="${index}"`;
            if (site.markdownFile) {
                dataAttrs += ` data-markdown-file="${site.markdownFile}"`;
            }
            if (site.url) {
                dataAttrs += ` data-url="${site.url}"`;
            }
            
            return `
                <div class="spotlight-result-item" ${dataAttrs}>
                    <div class="spotlight-result-icon">${site.icon || '🌐'}</div>
                    <div class="spotlight-result-content">
                        <div class="spotlight-result-title">
                            ${this.highlightMatch(site.name, query)}
                        </div>
                        <div class="spotlight-result-desc">
                            ${this.highlightMatch(site.description || '', query)}
                        </div>
                        <div class="spotlight-result-category">
                            ${site.categoryName || ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = resultsHtml;
        container.style.display = 'block';
        
        // 绑定点击事件
        container.querySelectorAll('.spotlight-result-item').forEach(item => {
            item.addEventListener('click', () => {
                this.selectSpotlightResult(item);
            });
        });
        
        // 默认选中第一个结果
        const firstItem = container.querySelector('.spotlight-result-item');
        if (firstItem) {
            firstItem.classList.add('selected');
            this.selectedIndex = 0;
        }
    }
    
    /**
     * 处理 Spotlight 键盘导航
     */
    handleSpotlightKeyboard(e, resultsContainer) {
        const resultItems = resultsContainer.querySelectorAll('.spotlight-result-item');
        const itemCount = resultItems.length;
        
        if (itemCount === 0) return;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, itemCount - 1);
                this.updateSpotlightSelection(resultItems);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, 0);
                this.updateSpotlightSelection(resultItems);
                break;
                
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && resultItems[this.selectedIndex]) {
                    this.selectSpotlightResult(resultItems[this.selectedIndex]);
                }
                break;
        }
    }
    
    /**
     * 更新 Spotlight 选中状态
     */
    updateSpotlightSelection(resultItems) {
        resultItems.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.classList.add('selected');
                item.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
            } else {
                item.classList.remove('selected');
            }
        });
    }
    
    /**
     * 选择 Spotlight 搜索结果
     */
    selectSpotlightResult(resultItem) {
        const url = resultItem.dataset.url;
        const markdownFile = resultItem.dataset.markdownFile;
        const siteId = resultItem.dataset.siteId;
        const siteName = resultItem.dataset.siteName;

        // 隐藏 Spotlight 搜索
        this.hideSpotlightSearch();

        // 检查链接和markdown内容的存在情况
        const hasUrl = url && url.trim() !== '';
        const hasMarkdown = markdownFile && markdownFile.trim() !== '';

        // 根据需求优先级处理：
        // 1. 同时存在链接和markdown时，优先打开链接
        // 2. 仅存在链接时，打开链接
        // 3. 仅存在markdown时，打开markdown
        if (hasUrl) {
            // 有链接时优先打开链接（无论是否同时有markdown）
            window.open(formatUrl(url), '_blank');
            // 记录点击统计
            this.recordSearchClick(siteId, document.getElementById('spotlightInput')?.value || '', 'external');
        } else if (hasMarkdown) {
            // 仅有markdown时打开文档
            if (this.navApp && this.navApp.markdownManager) {
                this.navApp.markdownManager.loadAndShow(markdownFile, siteName || '文档');
            }
            // 记录点击统计
            this.recordSearchClick(siteId, document.getElementById('spotlightInput')?.value || '', 'markdown');
        }
    }
    
    /**
     * 隐藏 Spotlight 搜索
     */
    hideSpotlightSearch() {
        const spotlightOverlay = document.getElementById('spotlightOverlay');
        if (spotlightOverlay) {
            spotlightOverlay.classList.remove('show');
            setTimeout(() => {
                spotlightOverlay.style.display = 'none';
            }, 200);
        }

        // 清空搜索输入框和结果
        const spotlightInput = document.getElementById('spotlightInput');
        const spotlightResults = document.getElementById('spotlightResults');
        if (spotlightInput) {
            spotlightInput.value = '';
        }
        if (spotlightResults) {
            spotlightResults.innerHTML = '';
            spotlightResults.style.display = 'none';
        }

        // 恢复背景滚动
        document.body.style.overflow = '';

        console.log('Spotlight 搜索已关闭');
    }
    
    /**
     * 高亮匹配内容
     */
    highlightMatch(text, query) {
        if (!text || !query) return text || '';

        // 清理可能存在的HTML标签，确保不会重复高亮
        const cleanText = this.stripHtmlTags(text);

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return cleanText.replace(regex, '<mark class="spotlight-highlight">$1</mark>');
    }
    
    /**
     * 添加 Spotlight 样式
     */
    addSpotlightStyles() {
        if (document.getElementById('spotlightStyles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'spotlightStyles';
        styles.textContent = `
            .spotlight-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                display: none;
                align-items: flex-start;
                justify-content: center;
                padding-top: 15vh;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.2s ease;
            }

            .spotlight-overlay.show {
                opacity: 1;
            }

            .spotlight-container {
                background: var(--card-background);
                border-radius: 12px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                width: 90%;
                max-width: 600px;
                max-height: 70vh;
                overflow: hidden;
                transform: scale(0.9);
                transition: transform 0.2s ease;
                border: 1px solid var(--border-color);
            }

            .spotlight-overlay.show .spotlight-container {
                transform: scale(1);
            }

            .spotlight-input {
                width: 100%;
                padding: 20px 24px;
                font-size: 18px;
                border: none;
                outline: none;
                background: transparent;
                color: var(--text-primary);
                font-weight: 400;
                border-bottom: 1px solid var(--border-color);
            }

            .spotlight-input::placeholder {
                color: var(--text-secondary);
                font-weight: 300;
            }

            .spotlight-results {
                max-height: 400px;
                overflow-y: auto;
                display: none;
            }

            .spotlight-result-item {
                display: flex;
                align-items: center;
                padding: 12px 24px;
                cursor: pointer;
                transition: background-color 0.15s ease;
                border-bottom: 1px solid var(--border-color);
            }

            .spotlight-result-item:last-child {
                border-bottom: none;
            }

            .spotlight-result-item:hover,
            .spotlight-result-item.selected {
                background: var(--primary-color);
                color: white;
            }

            .spotlight-result-item.selected .spotlight-result-desc,
            .spotlight-result-item.selected .spotlight-result-category {
                color: rgba(255, 255, 255, 0.8);
            }

            .spotlight-result-icon {
                font-size: 20px;
                margin-right: 16px;
                flex-shrink: 0;
                width: 24px;
                text-align: center;
            }

            .spotlight-result-content {
                flex: 1;
                min-width: 0;
            }

            .spotlight-result-title {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 4px;
                line-height: 1.3;
            }

            .spotlight-result-desc {
                font-size: 14px;
                color: var(--text-secondary);
                margin-bottom: 2px;
                line-height: 1.4;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .spotlight-result-category {
                font-size: 12px;
                color: var(--text-muted);
                font-weight: 500;
            }

            .spotlight-no-results {
                padding: 40px 24px;
                text-align: center;
                color: var(--text-muted);
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 12px;
            }

            .spotlight-highlight {
                background: rgba(34, 197, 94, 0.25);
                color: #16a34a;
                padding: 2px 4px;
                border-radius: 3px;
                font-weight: 600;
            }

            .spotlight-result-item.selected .spotlight-highlight {
                background: rgba(255, 255, 255, 0.3);
                color: white;
            }

            @media (max-width: 768px) {
                .spotlight-overlay {
                    padding-top: 10vh;
                }
                
                .spotlight-container {
                    width: 95%;
                    max-height: 80vh;
                }
                
                .spotlight-input {
                    padding: 16px 20px;
                    font-size: 16px;
                }
                
                .spotlight-result-item {
                    padding: 16px 20px;
                }
            }
        `;
        document.head.appendChild(styles);
    }
    
    /**
     * 更新搜索数据
     * @param {Array} data 网站数据
     */
    updateSearchData(data) {
        this.searchData = data;
        console.log(`Search data updated: ${data.length} sites`);
    }

    /**
     * 更新标签筛选器标题
     * @param {string} categoryId 分类ID
     * @param {number} tagCount 标签数量
     */
    updateTagFilterHeader(categoryId, tagCount) {
        // 查找或创建标题元素
        let header = this.tagFilters.parentElement.querySelector('.tag-filter-header');
        if (!header) {
            header = document.createElement('div');
            header.className = 'tag-filter-header';
            this.tagFilters.parentElement.insertBefore(header, this.tagFilters);
        }

        // 获取分类名称
        const categoryName = categoryId ?
            this.navApp?.flatCategories?.find(cat => cat.id === categoryId)?.name || '未知分类' :
            '全部分类';

        // 查找或创建标题内容区域
        let titleDiv = header.querySelector('.tag-filter-title');
        if (!titleDiv) {
            titleDiv = document.createElement('div');
            titleDiv.className = 'tag-filter-title';
            header.appendChild(titleDiv);
        }

        // 更新标题内容
        titleDiv.innerHTML = `
            <i class="fas fa-tags"></i>
            <span class="tag-filter-category">${categoryName}</span>
            <span class="tag-filter-count">${tagCount} 个标签</span>
        `;

        // 处理"显示全部"按钮
        let showAllBtn = header.querySelector('.tag-filter-show-all');

        if (categoryId) {
            // 需要显示"全部"按钮
            if (!showAllBtn) {
                showAllBtn = document.createElement('button');
                showAllBtn.className = 'tag-filter-show-all';
                showAllBtn.title = '显示所有标签';
                showAllBtn.innerHTML = `
                    <i class="fas fa-expand-arrows-alt"></i>
                    全部
                `;
                header.appendChild(showAllBtn);

                // 绑定事件（只绑定一次）
                showAllBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation(); // 阻止事件冒泡，防止触发外部点击隐藏逻辑
                    this.populateTagFilters('');
                });
            }
        } else {
            // 不需要显示"全部"按钮，移除它
            if (showAllBtn) {
                showAllBtn.remove();
            }
        }
    }

    /**
     * 同步当前页面分类到标签筛选器
     * @param {string} categoryId 分类ID
     */
    syncCategoryToTagFilter(categoryId) {
        // 如果筛选器没有手动设置分类，则与页面分类同步
        if (!this.filters.category && this.tagFilters) {
            console.log(`同步标签筛选器到分类: ${categoryId}`);
            this.populateTagFilters(categoryId);
        }
    }
    
    /**
     * 获取当前搜索查询
     * @returns {string} 当前搜索查询
     */
    getCurrentQuery() {
        return this.currentQuery;
    }
    
    /**
     * 检查是否在搜索状态
     * @returns {boolean} 是否在搜索状态
     */
    isSearching() {
        return !!this.currentQuery;
    }

    /**
     * 初始化筛选器
     */
    initializeFilters() {
        if (!this.categoryFilter || !this.tagFilters) {
            return;
        }

        // 检查标签筛选器使用状态
        this.checkTagFiltersUsage();

        // 初始化分类筛选器
        this.populateCategoryFilter();

        // 获取当前页面分类，优先显示当前分类的标签
        const currentCategory = this.navApp?.currentCategory || '';
        
        // 初始化标签筛选器（显示当前分类的标签）
        this.populateTagFilters(currentCategory);

        // 初始化搜索栏状态
        this.updateSearchBarFilterState();
    }

    /**
     * 初始化自定义下拉选择器
     */
    initCustomSelect() {
        const trigger = this.categoryFilter.querySelector('.custom-select-trigger');
        const options = this.categoryFilter.querySelector('.custom-select-options');

        if (!trigger || !options) return;

        // 点击触发器切换下拉状态
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleCustomSelect();
        });

        // 点击选项
        options.addEventListener('click', (e) => {
            const option = e.target.closest('.custom-select-option');
            if (option) {
                this.selectCustomOption(option);
            }
        });

        // 键盘导航支持
        this.categoryFilter.addEventListener('keydown', (e) => {
            this.handleCustomSelectKeyboard(e);
        });

        // 点击外部关闭
        document.addEventListener('click', (e) => {
            if (!this.categoryFilter.contains(e.target)) {
                this.closeCustomSelect();
            }
        });
    }

    /**
     * 切换自定义下拉选择器状态
     */
    toggleCustomSelect() {
        const isOpen = this.categoryFilter.classList.contains('open');
        if (isOpen) {
            this.closeCustomSelect();
        } else {
            this.openCustomSelect();
        }
    }

    /**
     * 打开自定义下拉选择器
     */
    openCustomSelect() {
        // 关闭其他可能打开的下拉框
        document.querySelectorAll('.custom-select.open').forEach(select => {
            if (select !== this.categoryFilter) {
                select.classList.remove('open');
            }
        });

        this.categoryFilter.classList.add('open');
        this.categoryFilter.setAttribute('aria-expanded', 'true');

        // 聚焦到触发器以支持键盘导航
        const trigger = this.categoryFilter.querySelector('.custom-select-trigger');
        if (trigger) {
            trigger.focus();
        }
    }

    /**
     * 关闭自定义下拉选择器
     */
    closeCustomSelect() {
        this.categoryFilter.classList.remove('open');
        this.categoryFilter.setAttribute('aria-expanded', 'false');
    }

    /**
     * 选择自定义下拉选项
     */
    selectCustomOption(option) {
        const value = option.dataset.value;
        const text = option.textContent;

        // 更新显示文本
        const textElement = this.categoryFilter.querySelector('.custom-select-text');
        if (textElement) {
            textElement.textContent = text;
        }

        // 更新活跃状态
        this.categoryFilter.querySelectorAll('.custom-select-option').forEach(opt => {
            opt.classList.remove('active');
        });
        option.classList.add('active');

        // 关闭下拉框
        this.closeCustomSelect();

        // 触发筛选逻辑
        this.filters.category = value;
        this.filters.tags.clear();
        this.populateTagFilters(value);
        this.applyFilters();
    }

    /**
     * 处理自定义下拉选择器键盘事件
     */
    handleCustomSelectKeyboard(e) {
        const isOpen = this.categoryFilter.classList.contains('open');
        const options = Array.from(this.categoryFilter.querySelectorAll('.custom-select-option'));

        switch (e.key) {
            case 'Enter':
            case ' ':
                e.preventDefault();
                if (!isOpen) {
                    this.openCustomSelect();
                } else {
                    // 选择当前高亮的选项
                    const activeOption = this.categoryFilter.querySelector('.custom-select-option.highlighted') ||
                                       this.categoryFilter.querySelector('.custom-select-option.active');
                    if (activeOption) {
                        this.selectCustomOption(activeOption);
                    }
                }
                break;

            case 'Escape':
                e.preventDefault();
                if (isOpen) {
                    this.closeCustomSelect();
                } else {
                    this.hideFilters();
                }
                break;

            case 'ArrowDown':
                e.preventDefault();
                if (!isOpen) {
                    this.openCustomSelect();
                } else {
                    this.highlightNextOption(options, 1);
                }
                break;

            case 'ArrowUp':
                e.preventDefault();
                if (isOpen) {
                    this.highlightNextOption(options, -1);
                }
                break;
        }
    }

    /**
     * 高亮下一个选项
     */
    highlightNextOption(options, direction) {
        const currentHighlighted = this.categoryFilter.querySelector('.custom-select-option.highlighted');
        let currentIndex = currentHighlighted ? options.indexOf(currentHighlighted) : -1;

        // 移除当前高亮
        if (currentHighlighted) {
            currentHighlighted.classList.remove('highlighted');
        }

        // 计算新索引
        currentIndex += direction;
        if (currentIndex < 0) currentIndex = options.length - 1;
        if (currentIndex >= options.length) currentIndex = 0;

        // 高亮新选项
        if (options[currentIndex]) {
            options[currentIndex].classList.add('highlighted');
            options[currentIndex].scrollIntoView({ block: 'nearest' });
        }
    }

    /**
     * 填充分类筛选器
     */
    populateCategoryFilter() {
        if (!this.categoryFilter || !this.navApp) return;

        const categories = this.navApp.flatCategories || [];
        const optionsContainer = this.categoryFilter.querySelector('.custom-select-options');

        if (!optionsContainer) return;

        const categoryOptions = categories.map(category =>
            `<div class="custom-select-option" data-value="${category.id}">${category.name}</div>`
        ).join('');

        optionsContainer.innerHTML = `
            <div class="custom-select-option active" data-value="">所有分类</div>
            ${categoryOptions}
        `;
    }

    /**
     * 获取指定分类下的所有标签
     * @param {string} categoryId 分类ID，为空时返回所有标签
     * @returns {Set} 标签集合
     */
    getTagsByCategory(categoryId = '') {
        if (!this.searchData) return new Set();

        const tags = new Set();
        this.searchData.forEach(site => {
            // 如果指定了分类，检查网站是否匹配该分类（包括子分类）
            if (categoryId && !this.siteMatchesCategory(site, categoryId)) {
                return;
            }

            if (site.tags && site.tags.length > 0) {
                site.tags.forEach(tag => tags.add(tag));
            }
        });

        return tags;
    }

    /**
     * 填充标签筛选器
     * @param {string} categoryId 可选的分类ID，用于筛选标签
     */
    populateTagFilters(categoryId = '') {
        if (!this.tagFilters || !this.searchData) return;

        // 根据分类收集标签
        const availableTags = this.getTagsByCategory(categoryId);

        // 添加标签筛选器标题和统计信息
        this.updateTagFilterHeader(categoryId, availableTags.size);

        if (availableTags.size === 0) {
            this.tagFilters.innerHTML = '<div class="no-tags-message">该分类下没有可用标签</div>';
            return;
        }

        // 性能优化：使用DocumentFragment减少DOM操作
        const fragment = document.createDocumentFragment();
        const sortedTags = Array.from(availableTags).sort();
        
        // 清空容器
        this.tagFilters.innerHTML = '';

        sortedTags.forEach(tag => {
            const button = document.createElement('button');
            button.className = 'tag-filter';
            button.dataset.tag = tag;
            button.textContent = tag;
            
            // 检查是否为活跃状态
            if (this.filters.tags.has(tag)) {
                button.classList.add('active');
            }

            // 绑定点击事件（使用事件委托优化性能）
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleTagFilterClick(tag, button);
            });

            // 添加键盘支持
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleTagFilterClick(tag, button);
                }
            });

            button.setAttribute('tabindex', '0');
            fragment.appendChild(button);
        });

        // 一次性添加所有按钮到DOM
        this.tagFilters.appendChild(fragment);
        
        // 添加键盘导航支持
        this.addTagFiltersKeyboardNavigation();
        
        // 显示使用提示（如果用户还没有使用过标签筛选器）
        if (this.isFiltersVisible) {
            this.showTagFiltersHint();
        }
        
        // 显示标签统计信息
        const tagCountInfo = categoryId ? 
            `显示 ${availableTags.size} 个该分类下的标签` : 
            `显示全部 ${availableTags.size} 个标签`;
        
        console.log(`标签筛选器更新: ${tagCountInfo}`);
    }

    /**
     * 处理标签筛选器点击事件
     * @param {string} tag 标签名称
     * @param {Element} button 按钮元素
     */
    handleTagFilterClick(tag, button) {
        // 标记用户已使用标签筛选器
        this.markTagFiltersAsUsed();
        
        this.toggleTagFilter(tag);
        button.classList.toggle('active');
        
        // 添加点击动画效果
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 100);
    }

    /**
     * 显示筛选器使用提示
     */
    showTagFiltersHint() {
        if (!this.searchFilters || this.hasUsedTagFilters) return;
        
        // 查找或创建提示元素
        let hintElement = this.searchFilters.querySelector('.filter-usage-hint');
        if (!hintElement) {
            hintElement = document.createElement('div');
            hintElement.className = 'filter-usage-hint';
            hintElement.innerHTML = `
                <div class="hint-content">
                    <i class="fas fa-lightbulb hint-icon"></i>
                    <span class="hint-text">使用 ←→↑↓ 导航标签，Enter/Space 选择，点击任意标签开始使用</span>
                </div>
            `;
            
            // 插入到筛选器头部后面
            const header = this.searchFilters.querySelector('.search-filters-header');
            if (header) {
                header.insertAdjacentElement('afterend', hintElement);
            }
        }
        
        hintElement.style.display = 'block';
        hintElement.classList.add('show');
    }

    /**
     * 隐藏筛选器使用提示
     */
    hideTagFiltersHint() {
        if (!this.searchFilters) return;
        
        const hintElement = this.searchFilters.querySelector('.filter-usage-hint');
        if (hintElement) {
            hintElement.classList.remove('show');
            setTimeout(() => {
                hintElement.style.display = 'none';
            }, 300); // 等待动画完成
        }
    }

    /**
     * 标记标签筛选器已被使用
     */
    markTagFiltersAsUsed() {
        if (this.hasUsedTagFilters) return;
        
        this.hasUsedTagFilters = true;
        this.hideTagFiltersHint();
        
        // 保存到本地存储，避免重复显示
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem('tagFiltersUsed', 'true');
        }
    }

    /**
     * 检查标签筛选器是否已被使用过
     */
    checkTagFiltersUsage() {
        if (typeof localStorage !== 'undefined') {
            this.hasUsedTagFilters = localStorage.getItem('tagFiltersUsed') === 'true';
        }
    }

    /**
     * 重置标签筛选器使用状态（用于测试）
     */
    resetTagFiltersUsage() {
        this.hasUsedTagFilters = false;
        if (typeof localStorage !== 'undefined') {
            localStorage.removeItem('tagFiltersUsed');
        }
        
        // 移除现有的提示元素，强制重新创建
        if (this.searchFilters) {
            const existingHint = this.searchFilters.querySelector('.filter-usage-hint');
            if (existingHint) {
                existingHint.remove();
            }
        }
        
        // 如果筛选器当前可见，重新显示提示
        if (this.isFiltersVisible) {
            this.showTagFiltersHint();
        }
        
        console.log('标签筛选器使用状态已重置');
    }

    /**
     * 为标签筛选器添加键盘导航支持
     */
    addTagFiltersKeyboardNavigation() {
        if (!this.tagFilters) return;

        // 检查使用状态
        this.checkTagFiltersUsage();

        // 添加focusin事件，处理键盘导航
        this.tagFilters.addEventListener('focusin', (e) => {
            if (e.target.classList.contains('tag-filter')) {
                // 标记为已使用
                this.markTagFiltersAsUsed();
            }
        });

        this.tagFilters.addEventListener('keydown', (e) => {
            const tagButtons = Array.from(this.tagFilters.querySelectorAll('.tag-filter'));
            const currentIndex = tagButtons.findIndex(btn => btn === document.activeElement);
            
            // 标记为已使用（任何键盘操作）
            this.markTagFiltersAsUsed();
            
            switch (e.key) {
                case 'ArrowRight':
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = (currentIndex + 1) % tagButtons.length;
                    if (tagButtons[nextIndex]) {
                        tagButtons[nextIndex].focus();
                    }
                    break;
                    
                case 'ArrowLeft':
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex <= 0 ? tagButtons.length - 1 : currentIndex - 1;
                    if (tagButtons[prevIndex]) {
                        tagButtons[prevIndex].focus();
                    }
                    break;
                    
                case 'Home':
                    e.preventDefault();
                    if (tagButtons[0]) {
                        tagButtons[0].focus();
                    }
                    break;
                    
                case 'End':
                    e.preventDefault();
                    if (tagButtons[tagButtons.length - 1]) {
                        tagButtons[tagButtons.length - 1].focus();
                    }
                    break;
                    
                case 'Escape':
                    e.preventDefault();
                    // 失去焦点并隐藏筛选器
                    document.activeElement.blur();
                    this.hideFilters();
                    break;
            }
        });
    }

    /**
     * 切换筛选器显示
     */
    toggleFilters() {
        if (!this.searchFilters || !this.searchFilterBtn) {
            console.error('搜索筛选器元素未找到');
            return;
        }

        this.isFiltersVisible = !this.isFiltersVisible;

        // 移动端特殊处理
        const isMobile = window.innerWidth <= 767;

        if (this.isFiltersVisible) {
            this.searchFilters.style.display = 'block';
            this.searchFilterBtn.classList.add('active');

            // 移动端额外处理
            if (isMobile) {
                // 强制设置移动端样式
                this.searchFilters.style.position = 'fixed';
                this.searchFilters.style.top = 'var(--navbar-height)';
                this.searchFilters.style.left = '0';
                this.searchFilters.style.right = '0';
                this.searchFilters.style.bottom = '0';
                this.searchFilters.style.zIndex = '1060';
                this.searchFilters.style.width = '100vw';
                this.searchFilters.style.height = '100vh';

                // 阻止背景滚动
                document.body.style.overflow = 'hidden';

                console.log('移动端筛选器已显示');
            }

            this.hideSearchResults();
            // 显示使用提示
            this.showTagFiltersHint();
            // 添加移动端关闭按钮
            this.addMobileCloseButton();
        } else {
            this.searchFilters.style.display = 'none';
            this.searchFilterBtn.classList.remove('active');

            // 隐藏使用提示
            this.hideTagFiltersHint();
            // 恢复背景滚动
            if (isMobile) {
                document.body.style.overflow = '';
                console.log('移动端筛选器已隐藏');
            }
        }
    }

    /**
     * 隐藏筛选器
     */
    hideFilters() {
        if (!this.searchFilters || !this.searchFilterBtn) return;

        this.isFiltersVisible = false;
        this.searchFilters.style.display = 'none';
        this.searchFilterBtn.classList.remove('active');

        // 隐藏使用提示
        this.hideTagFiltersHint();

        // 移动端特殊处理
        const isMobile = window.innerWidth <= 767;
        if (isMobile) {
            // 恢复背景滚动
            document.body.style.overflow = '';

            // 移除移动端关闭按钮
            const closeBtn = this.searchFilters.querySelector('.search-filters-close');
            if (closeBtn) {
                closeBtn.remove();
            }

            console.log('移动端筛选器已完全隐藏');
        }
    }

    /**
     * 添加移动端关闭按钮
     */
    addMobileCloseButton() {
        if (window.innerWidth > 767) return;

        // 检查是否已经存在关闭按钮
        const existingCloseBtn = this.searchFilters.querySelector('.search-filters-close');
        if (existingCloseBtn) {
            console.log('移动端关闭按钮已存在');
            return;
        }

        // 创建关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.className = 'search-filters-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.title = '关闭筛选器';
        closeBtn.setAttribute('aria-label', '关闭筛选器');

        // 添加点击事件
        closeBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.hideFilters();
        });

        // 添加触摸事件支持
        closeBtn.addEventListener('touchend', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.hideFilters();
        }, { passive: false });

        // 添加键盘支持
        closeBtn.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.hideFilters();
            }
        });

        // 将按钮添加到筛选器头部
        const header = this.searchFilters.querySelector('.search-filters-header');
        if (header) {
            header.appendChild(closeBtn);
            console.log('移动端关闭按钮已添加');
        } else {
            console.error('筛选器头部未找到，无法添加关闭按钮');
        }
    }

    /**
     * 切换标签筛选
     * @param {string} tag 标签名称
     */
    toggleTagFilter(tag) {
        if (this.filters.tags.has(tag)) {
            this.filters.tags.delete(tag);
        } else {
            this.filters.tags.add(tag);
        }
        this.applyFilters();
    }

    /**
     * 应用筛选器
     */
    applyFilters() {
        // 更新搜索栏筛选状态
        this.updateSearchBarFilterState();

        if (this.currentQuery) {
            this.performSearch(this.currentQuery);
        } else {
            // 如果没有搜索查询但有筛选条件，显示筛选后的所有结果
            if (this.filters.category || this.filters.tags.size > 0) {
                this.showFilteredResults();
            }
        }
    }

    /**
     * 显示筛选后的结果（无搜索查询时）
     */
    showFilteredResults() {
        if (!this.searchData || this.searchData.length === 0) {
            return;
        }

        const filteredSites = this.searchData.filter(site => this.passesFilters(site));

        if (filteredSites.length === 0) {
            this.displaySearchResults([], '');
        } else {
            // 将筛选结果转换为搜索结果格式
            const results = filteredSites.map(site => ({
                ...site,
                score: 100,
                matchType: 'filter'
            }));
            this.displaySearchResults(results, '');
        }

        this.showSearchResults();
    }

    /**
     * 检查是否为Shift+快捷键组合
     * @param {KeyboardEvent} e 键盘事件
     * @param {string} key 按键
     * @returns {boolean} 是否匹配
     */
    isShiftShortcut(e, key) {
        if (typeof Platform === 'undefined') return false;
        const modifierPressed = Platform.isMac ? e.metaKey : e.ctrlKey;
        return modifierPressed && e.shiftKey && e.key.toLowerCase() === key.toLowerCase();
    }

    /**
     * 清除所有筛选器
     */
    clearAllFilters() {
        this.filters.category = '';
        this.filters.tags.clear();

        // 重置自定义下拉选择器UI状态
        if (this.categoryFilter) {
            // 重置显示文本
            const textElement = this.categoryFilter.querySelector('.custom-select-text');
            if (textElement) {
                textElement.textContent = '所有分类';
            }

            // 重置活跃状态
            this.categoryFilter.querySelectorAll('.custom-select-option').forEach(opt => {
                opt.classList.remove('active');
            });

            // 设置"所有分类"为活跃状态
            const allCategoriesOption = this.categoryFilter.querySelector('.custom-select-option[data-value=""]');
            if (allCategoriesOption) {
                allCategoriesOption.classList.add('active');
            }

            // 关闭下拉框
            this.closeCustomSelect();

            // 兼容原生select（如果存在）
            if (this.categoryFilter.tagName === 'SELECT') {
                this.categoryFilter.value = '';
            }
        }

        // 重新填充标签筛选器，显示所有标签
        this.populateTagFilters('');

        // 更新搜索栏状态
        this.updateSearchBarFilterState();

        this.applyFilters();
    }

    /**
     * 完整清除（包括搜索内容和筛选条件）
     */
    clearAll() {
        // 检查是否有内容需要清除
        const hasFilters = this.filters.category || this.filters.tags.size > 0;
        const hasSearchQuery = this.currentQuery && this.currentQuery.trim() !== '';

        if (!hasFilters && !hasSearchQuery) {
            return false; // 没有内容需要清除
        }

        // 清除搜索内容
        if (hasSearchQuery) {
            this.clearSearch();
        }

        // 清除筛选条件
        if (hasFilters) {
            this.clearAllFilters();
        }

        return true; // 已执行清除操作
    }

    /**
     * 带反馈的清除所有筛选器
     */
    clearAllFiltersWithFeedback() {
        // 检查是否有搜索内容或筛选条件需要清除
        const hasFilters = this.filters.category || this.filters.tags.size > 0;
        const hasSearchQuery = this.currentQuery && this.currentQuery.trim() !== '';
        
        if (!hasFilters && !hasSearchQuery) {
            // 没有任何内容需要清除时的提示
            this.showTemporaryMessage('没有搜索内容或筛选条件需要清除', 'info');
            return;
        }

        // 记录清除前的状态用于提示
        const categoryCount = this.filters.category ? 1 : 0;
        const tagCount = this.filters.tags.size;
        const totalFilters = categoryCount + tagCount;
        const hasSearch = hasSearchQuery ? 1 : 0;

        // 同时清除搜索内容和筛选条件
        if (hasSearchQuery) {
            this.clearSearch();
        }
        
        if (hasFilters) {
            this.clearAllFilters();
        }

        // 添加清除动画效果
        this.animateClearAction();

        // 生成反馈消息
        let message = '';
        if (hasSearch && totalFilters > 0) {
            message = `已清除搜索内容和 ${totalFilters} 个筛选条件`;
        } else if (hasSearch) {
            message = '已清除搜索内容';
        } else {
            message = `已清除 ${totalFilters} 个筛选条件`;
        }

        // 显示成功反馈
        this.showTemporaryMessage(message, 'success');

        // 记录操作
        console.log(`快捷键完整清除: 搜索(${hasSearch ? '是' : '否'}), 筛选条件 ${totalFilters} 个 (分类: ${categoryCount}, 标签: ${tagCount})`);
    }

    /**
     * 清除操作的动画效果
     */
    animateClearAction() {
        // 为筛选按钮添加清除动画
        if (this.searchFilterBtn) {
            this.searchFilterBtn.style.transform = 'scale(0.9)';
            this.searchFilterBtn.style.transition = 'transform 0.2s ease';
            
            setTimeout(() => {
                this.searchFilterBtn.style.transform = '';
                setTimeout(() => {
                    this.searchFilterBtn.style.transition = '';
                }, 200);
            }, 200);
        }

        // 为搜索栏添加重置动画
        if (this.searchWrapper) {
            this.searchWrapper.style.boxShadow = '0 0 0 2px var(--success-color)';
            setTimeout(() => {
                this.searchWrapper.style.boxShadow = '';
            }, 500);
        }
    }

    /**
     * 更新筛选按钮标题
     */
    updateFilterButtonTitle() {
        if (!this.searchFilterBtn) return;
        
        const shortcutText = (typeof Platform !== 'undefined') ? 
            Platform.getShortcutText('Shift+K') : 'Ctrl+Shift+K';
        
        const baseTitle = '搜索筛选';
        const shortcutHint = `快捷键: ${shortcutText} 快速清除筛选`;
        
        this.searchFilterBtn.title = `${baseTitle}\n${shortcutHint}`;
    }

    /**
     * 显示临时消息
     * @param {string} message 消息内容
     * @param {string} type 消息类型 ('success', 'info', 'warning', 'error')
     */
    showTemporaryMessage(message, type = 'info') {
        // 尝试使用全局Toast功能
        if (typeof showToast === 'function') {
            showToast(message, type, 2000);
            return;
        }

        // 创建临时消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `search-temp-message search-temp-message-${type}`;
        messageEl.textContent = message;

        // 添加到页面body
        document.body.appendChild(messageEl);

        // 自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 2000);
    }

    /**
     * 检查网站是否通过筛选器
     * @param {Object} site 网站对象
     * @returns {boolean} 是否通过筛选
     */
    passesFilters(site) {
        // 分类筛选
        if (this.filters.category && !this.siteMatchesCategory(site, this.filters.category)) {
            return false;
        }

        // 标签筛选
        if (this.filters.tags.size > 0) {
            if (!site.tags || site.tags.length === 0) {
                return false;
            }

            // 检查是否包含任一选中的标签
            const hasMatchingTag = Array.from(this.filters.tags).some(tag =>
                site.tags.includes(tag)
            );

            if (!hasMatchingTag) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查网站是否匹配指定分类（包括子分类匹配）
     * @param {Object} site 网站对象
     * @param {string} categoryId 分类ID
     * @returns {boolean} 是否匹配
     */
    siteMatchesCategory(site, categoryId) {
        if (!site.categoryId || !categoryId) {
            return false;
        }

        // 直接匹配
        if (site.categoryId === categoryId) {
            return true;
        }

        // 检查是否是子分类匹配（网站属于选中分类的子分类）
        if (this.navApp && this.navApp.flatCategories) {
            const siteCategory = this.navApp.flatCategories.find(cat => cat.id === site.categoryId);
            if (siteCategory) {
                // 递归检查父分类链
                let currentParentId = siteCategory.parentId;
                while (currentParentId) {
                    if (currentParentId === categoryId) {
                        return true;
                    }
                    const parentCategory = this.navApp.flatCategories.find(cat => cat.id === currentParentId);
                    currentParentId = parentCategory ? parentCategory.parentId : null;
                }
            }
        }

        return false;
    }

    /**
     * 更新搜索栏筛选状态
     */
    updateSearchBarFilterState() {
        if (!this.searchWrapper) return;

        const hasFilters = this.filters.category || this.filters.tags.size > 0;

        // 更新搜索包装器状态
        this.searchWrapper.classList.toggle('filtered', hasFilters);

        // 更新筛选按钮状态
        if (this.searchFilterBtn) {
            this.searchFilterBtn.classList.toggle('has-filters', hasFilters);

            // 更新按钮标题
            if (hasFilters) {
                const filterCount = (this.filters.category ? 1 : 0) + this.filters.tags.size;
                this.searchFilterBtn.title = `搜索筛选 (${filterCount} 个筛选条件)`;
            } else {
                this.searchFilterBtn.title = '搜索筛选';
            }
        }

        // 更新搜索框占位符文本
        if (this.searchInput) {
            if (hasFilters) {
                const filterCount = (this.filters.category ? 1 : 0) + this.filters.tags.size;
                this.searchInput.placeholder = `搜索网站... (已应用 ${filterCount} 个筛选条件)`;
            } else {
                // 使用平台检测来显示正确的快捷键
                const shortcutText = (typeof Platform !== 'undefined') ? Platform.getShortcutText('K') : 'Ctrl+K';
                this.searchInput.placeholder = `搜索网站名称、描述、标签... ${shortcutText}`;
            }
        }
    }
}

// 添加搜索相关的CSS样式
const searchStyles = `
.search-result-item.selected {
    background-color: var(--primary-color) !important;
    color: white !important;
}

.search-result-item.selected .search-result-desc,
.search-result-item.selected .search-result-category {
    color: rgba(255, 255, 255, 0.8) !important;
}

.search-result-item.selected mark {
    background-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

.search-no-results {
    padding: 20px;
    text-align: center;
    color: var(--text-muted);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.search-no-results i {
    font-size: 24px;
    margin-bottom: 4px;
}

mark {
    background-color: var(--primary-color);
    color: white;
    padding: 1px 2px;
    border-radius: 2px;
}

[data-theme="dark"] mark {
    background-color: #60a5fa;
    color: #1e293b;
}

.no-tags-message {
    padding: 15px;
    text-align: center;
    color: var(--text-muted);
    background: var(--card-background);
    border: 1px dashed var(--border-color);
    border-radius: 8px;
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.no-tags-message::before {
    content: "🏷️";
    font-size: 16px;
}

.tag-filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 10px;
}

.tag-filter-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-primary);
}

.tag-filter-title i {
    color: var(--primary-color);
}

.tag-filter-category {
    font-weight: 600;
}

.tag-filter-count {
    color: var(--text-secondary);
    font-size: 12px;
    background: var(--card-background);
    padding: 2px 8px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.tag-filter-show-all {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tag-filter-show-all:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.tag-filter-show-all i {
    font-size: 10px;
}

/* 标签筛选器按钮样式增强 */
.tag-filter {
    transition: all 0.2s ease;
    outline: none;
}

.tag-filter:focus {
    box-shadow: 0 0 0 2px var(--primary-color);
    z-index: 1;
    position: relative;
}

.tag-filter:focus:not(:active) {
    transform: translateY(-1px);
}

/* 筛选器使用提示 */
.filter-usage-hint {
    display: none;
    margin: 12px 0;
    padding: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.filter-usage-hint.show {
    display: block;
    animation: slideDownSmooth 0.4s ease;
}

.hint-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 14px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #0ea5e9;
    border-radius: 8px;
    color: #0c4a6e;
    font-size: 13px;
    line-height: 1.4;
    box-shadow: 0 2px 8px rgba(14, 165, 233, 0.1);
}

.hint-icon {
    color: #0ea5e9;
    font-size: 14px;
    flex-shrink: 0;
    animation: pulse 2s infinite;
}

.hint-text {
    flex: 1;
    font-weight: 500;
}

/* 优化的显示动画 */
@keyframes slideDownSmooth {
    from { 
        opacity: 0; 
        transform: translateY(-15px); 
        max-height: 0;
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
        max-height: 60px;
    }
}

@keyframes pulse {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1);
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 临时消息样式 */
.search-temp-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    animation: slideInRight 0.3s ease, slideOutRight 0.3s ease 1.7s forwards;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 300px;
}

.search-temp-message-success {
    background: var(--success-color, #10b981);
    color: white;
}

.search-temp-message-success::before {
    content: "✓";
    font-weight: bold;
}

.search-temp-message-info {
    background: var(--info-color, #3b82f6);
    color: white;
}

.search-temp-message-info::before {
    content: "ℹ";
    font-weight: bold;
}

.search-temp-message-warning {
    background: var(--warning-color, #f59e0b);
    color: white;
}

.search-temp-message-warning::before {
    content: "⚠";
    font-weight: bold;
}

.search-temp-message-error {
    background: var(--error-color, #ef4444);
    color: white;
}

.search-temp-message-error::before {
    content: "✕";
    font-weight: bold;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
`;

// 添加样式到页面
if (typeof document !== 'undefined') {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = searchStyles;
    document.head.appendChild(styleSheet);
}

// 导出搜索管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchManager;
} 