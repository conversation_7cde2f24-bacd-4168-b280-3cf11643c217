<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索高亮测试</title>
    <link rel="stylesheet" href="nav/css/style.css">
    <link rel="stylesheet" href="nav/css/themes.css">
    <style>
        body {
            padding: 40px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--card-background);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
        }
        
        .test-content {
            margin-bottom: 16px;
            color: var(--text-secondary);
        }
        
        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .theme-switcher select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--card-background);
            color: var(--text-primary);
        }
        
        .highlight-demo {
            padding: 12px;
            background: var(--surface-color);
            border-radius: 6px;
            margin: 8px 0;
        }
    </style>
</head>
<body data-theme="jasmine-green">
    <div class="theme-switcher">
        <select id="themeSelect">
            <option value="jasmine-green">清雅茉莉绿</option>
            <option value="ivory-light">日光象牙白</option>
            <option value="dark-obsidian">夜月玄玉黑</option>
            <option value="navy-blue">深邃海军蓝</option>
            <option value="star-moonlight-blue">星月流光蓝</option>
            <option value="light">浅色主题</option>
            <option value="dark">深色主题</option>
        </select>
    </div>

    <h1>搜索高亮与文本选中冲突修复测试</h1>

    <div class="test-section">
        <div class="test-title">1. 文本选中测试</div>
        <div class="test-content">
            请用鼠标选中这段文字，查看选中高亮效果。选中的文字应该有绿色背景（或根据主题变化），与搜索高亮区分开来。
            这是一段较长的文字，用于测试文本选中的视觉效果。
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">2. 搜索高亮测试</div>
        <div class="test-content">
            这里有一些<mark>搜索高亮</mark>的示例文字。<mark>高亮</mark>的文字应该有绿色背景和深绿色文字。
            多个<mark>关键词</mark>可以同时<mark>高亮显示</mark>。
        </div>
        <div class="highlight-demo">
            <strong>管道搜索高亮示例：</strong><br>
            <span class="pipeline-highlight-1">第一个关键词</span> | 
            <span class="pipeline-highlight-2">第二个关键词</span> | 
            <span class="pipeline-highlight-3">第三个关键词</span>
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">3. 混合场景测试</div>
        <div class="test-content">
            在这段文字中，你可以选中包含<mark>搜索高亮</mark>的文字，测试两种高亮效果的叠加情况。
            请尝试选中"<mark>搜索高亮</mark>"这几个字，看看视觉效果是否清晰可辨。
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">4. 长文本测试</div>
        <div class="test-content">
            这是一段很长的文字，用于测试在长文本中的<mark>搜索</mark>和选中效果。
            当用户在搜索结果中选中文字时，应该能够清楚地区分哪些是<mark>搜索匹配</mark>的内容，
            哪些是用户<mark>选中</mark>的内容。这对于用户体验来说非常重要，
            因为用户需要能够快速识别和操作相关内容。
        </div>
    </div>

    <script>
        // 主题切换功能
        const themeSelect = document.getElementById('themeSelect');
        const body = document.body;

        themeSelect.addEventListener('change', function() {
            body.setAttribute('data-theme', this.value);
        });

        // 添加一些交互提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('搜索高亮测试页面已加载');
            console.log('请尝试：');
            console.log('1. 切换不同主题');
            console.log('2. 选中不同的文字段落');
            console.log('3. 观察搜索高亮与文本选中的视觉区别');
        });
    </script>
</body>
</html>
